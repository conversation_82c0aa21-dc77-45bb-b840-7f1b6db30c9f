import React, { useRef, useEffect, useState, useMemo } from 'react';
import { TrackRefContext } from '@livekit/components-react';
import './GridLayoutCustom.scss';

/**
 * Custom GridLayout Component
 * Based on LiveKit's GridLayout but with full customization control
 *
 * Features:
 * - Custom grid calculations
 * - Pagination support
 * - Responsive design
 * - Visual stability
 * - Swipe gestures
 * - Custom styling
 */

// Grid layout definitions - you can customize these
const GRID_LAYOUTS = [
  { columns: 1, rows: 1, name: '1x1', minTiles: 1, maxTiles: 1 },
  { columns: 2, rows: 1, name: '2x1', minTiles: 2, maxTiles: 2 },
  { columns: 2, rows: 2, name: '2x2', minTiles: 3, maxTiles: 4 },
  { columns: 3, rows: 2, name: '3x2', minTiles: 5, maxTiles: 6 },
  { columns: 3, rows: 3, name: '3x3', minTiles: 7, maxTiles: 9 },
  { columns: 4, rows: 3, name: '4x3', minTiles: 10, maxTiles: 12 },
  { columns: 4, rows: 4, name: '4x4', minTiles: 13, maxTiles: 16 },
  { columns: 5, rows: 4, name: '5x4', minTiles: 17, maxTiles: 20 },
  { columns: 5, rows: 5, name: '5x5', minTiles: 21, maxTiles: 25 },
];

// Custom hook for container size
function useContainerSize(elementRef) {
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!elementRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(elementRef.current);
    return () => resizeObserver.disconnect();
  }, [elementRef]);

  return size;
}

// Custom hook for grid layout calculation
function useCustomGridLayout(containerRef, trackCount) {
  const { width, height } = useContainerSize(containerRef);

  const layout = useMemo(() => {
    if (width === 0 || height === 0) {
      return GRID_LAYOUTS[0]; // Default 1x1
    }

    // Find the best layout for the given track count and container size
    let bestLayout = GRID_LAYOUTS[0];

    for (const layout of GRID_LAYOUTS) {
      if (trackCount <= layout.maxTiles) {
        // Calculate if this layout fits in the container
        const tileWidth = width / layout.columns;
        const tileHeight = height / layout.rows;
        const aspectRatio = tileWidth / tileHeight;

        // Prefer layouts with aspect ratio closer to 16:9 (1.78)
        if (aspectRatio >= 0.5 && aspectRatio <= 3.0) {
          bestLayout = layout;
          break;
        }
      }
    }

    return bestLayout;
  }, [width, height, trackCount]);

  // Set CSS custom properties for grid
  useEffect(() => {
    if (containerRef.current && layout) {
      containerRef.current.style.setProperty('--grid-columns', layout.columns.toString());
      containerRef.current.style.setProperty('--grid-rows', layout.rows.toString());
    }
  }, [containerRef, layout]);

  return {
    layout,
    containerWidth: width,
    containerHeight: height,
  };
}

// Custom hook for pagination
function useCustomPagination(itemsPerPage, tracks) {
  const [currentPage, setCurrentPage] = useState(1);

  const totalPageCount = Math.max(Math.ceil(tracks.length / itemsPerPage), 1);

  // Auto-adjust current page if it exceeds total pages
  useEffect(() => {
    if (currentPage > totalPageCount) {
      setCurrentPage(totalPageCount);
    }
  }, [currentPage, totalPageCount]);

  const lastItemIndex = currentPage * itemsPerPage;
  const firstItemIndex = lastItemIndex - itemsPerPage;

  const nextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPageCount));
  };

  const prevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= totalPageCount) {
      setCurrentPage(pageNumber);
    }
  };

  const tracksOnPage = tracks.slice(firstItemIndex, lastItemIndex);

  return {
    totalPageCount,
    currentPage,
    nextPage,
    prevPage,
    goToPage,
    tracks: tracksOnPage,
    firstItemIndex,
    lastItemIndex,
  };
}

// Custom hook for swipe gestures
function useSwipeGestures(elementRef, { onLeftSwipe, onRightSwipe }) {
  useEffect(() => {
    if (!elementRef.current) return;

    let startX = 0;
    let startY = 0;
    const minSwipeDistance = 50;

    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e) => {
      if (!startX || !startY) return;

      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;

      // Check if horizontal swipe is more significant than vertical
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
        if (deltaX > 0) {
          onRightSwipe?.();
        } else {
          onLeftSwipe?.();
        }
      }

      startX = 0;
      startY = 0;
    };

    const element = elementRef.current;
    element.addEventListener('touchstart', handleTouchStart);
    element.addEventListener('touchend', handleTouchEnd);

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [elementRef, onLeftSwipe, onRightSwipe]);
}

// Pagination Controls Component
function PaginationControls({ currentPage, totalPageCount, nextPage, prevPage, goToPage }) {
  if (totalPageCount <= 1) return null;

  return (
    <div className="custom-pagination-controls">
      <button
        className="pagination-btn prev"
        onClick={prevPage}
        disabled={currentPage === 1}
        aria-label="Previous page"
      >
        ‹
      </button>

      <div className="pagination-indicators">
        {Array.from({ length: totalPageCount }, (_, i) => i + 1).map((pageNum) => (
          <button
            key={pageNum}
            className={`pagination-indicator ${currentPage === pageNum ? 'active' : ''}`}
            onClick={() => goToPage(pageNum)}
            aria-label={`Go to page ${pageNum}`}
          >
            {pageNum}
          </button>
        ))}
      </div>

      <button
        className="pagination-btn next"
        onClick={nextPage}
        disabled={currentPage === totalPageCount}
        aria-label="Next page"
      >
        ›
      </button>
    </div>
  );
}

// Track Item Component
function TrackItem({ trackRef, children }) {
  return (
    <TrackRefContext.Provider value={trackRef}>
      <div className="custom-grid-item">
        {children}
      </div>
    </TrackRefContext.Provider>
  );
}

// Main GridLayoutCustom Component
function GridLayoutCustom({
  tracks = [],
  children,
  className = '',
  enablePagination = true,
  enableSwipeGestures = true,
  customGridLayouts = null,
  maxTilesPerPage = null,
  ...htmlProps
}) {
  const gridRef = useRef(null);

  // Use custom grid layout hook
  const { layout, containerWidth, containerHeight } = useCustomGridLayout(gridRef, tracks.length);

  // Calculate max tiles per page
  const maxTiles = maxTilesPerPage || layout.maxTiles;

  // Use pagination if enabled and needed
  const pagination = useCustomPagination(maxTiles, tracks);
  const shouldShowPagination = enablePagination && tracks.length > maxTiles;

  // Use tracks from pagination or all tracks
  const displayTracks = shouldShowPagination ? pagination.tracks : tracks;

  // Setup swipe gestures if enabled
  useSwipeGestures(gridRef, {
    onLeftSwipe: shouldShowPagination ? pagination.nextPage : undefined,
    onRightSwipe: shouldShowPagination ? pagination.prevPage : undefined,
  });

  console.log('🔧 GridLayoutCustom render:', {
    totalTracks: tracks.length,
    displayTracks: displayTracks.length,
    layout: layout.name,
    currentPage: pagination.currentPage,
    totalPages: pagination.totalPageCount,
    containerSize: { width: containerWidth, height: containerHeight }
  });

  return (
    <div
      ref={gridRef}
      className={`custom-grid-layout ${className}`}
      data-layout={layout.name}
      data-has-pagination={shouldShowPagination}
      {...htmlProps}
    >
      <div className="custom-grid-container">
        {displayTracks.map((trackRef, index) => {
          // Generate a unique key for each track
          const key = trackRef?.participant?.identity ||
                     trackRef?.publication?.trackSid ||
                     `track-${index}`;

          return (
            <TrackItem key={key} trackRef={trackRef}>
              {children}
            </TrackItem>
          );
        })}
      </div>

      {shouldShowPagination && (
        <PaginationControls
          currentPage={pagination.currentPage}
          totalPageCount={pagination.totalPageCount}
          nextPage={pagination.nextPage}
          prevPage={pagination.prevPage}
          goToPage={pagination.goToPage}
        />
      )}
    </div>
  );
}

export default GridLayoutCustom;