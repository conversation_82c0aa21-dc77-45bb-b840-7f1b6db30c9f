/* Custom Grid Layout Styles */
.custom-grid-layout {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* CSS Custom Properties for dynamic grid */
  --grid-columns: 1;
  --grid-rows: 1;
  --grid-gap: 8px;
  --grid-item-aspect-ratio: 16/9;

  .custom-grid-container {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(var(--grid-columns), 1fr);
    grid-template-rows: repeat(var(--grid-rows), 1fr);
    gap: var(--grid-gap);
    padding: var(--grid-gap);
    width: 100%;
    height: 100%;
    box-sizing: border-box;

    .custom-grid-item {
      position: relative;
      width: 100%;
      height: 100%;
      min-height: 0; /* Important for grid items */
      min-width: 0;
      border-radius: 8px;
      overflow: hidden;
      background: #1a1a1a;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        z-index: 10;
      }

      /* Ensure child components fill the container */
      > * {
        width: 100%;
        height: 100%;
      }
    }
  }

  /* Pagination Controls */
  .custom-pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .pagination-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      &.prev {
        margin-right: 8px;
      }

      &.next {
        margin-left: 8px;
      }
    }

    .pagination-indicators {
      display: flex;
      gap: 8px;

      .pagination-indicator {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        &.active {
          background: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
        }
      }
    }
  }

  /* Layout-specific styles */
  &[data-layout="1x1"] {
    .custom-grid-item {
      border-radius: 12px;
    }
  }

  &[data-layout="2x2"] {
    --grid-gap: 6px;
  }

  &[data-layout="3x3"] {
    --grid-gap: 4px;
  }

  &[data-layout="4x4"],
  &[data-layout="5x4"],
  &[data-layout="5x5"] {
    --grid-gap: 2px;
    
    .custom-grid-item {
      border-radius: 4px;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    --grid-gap: 4px;

    .custom-grid-container {
      padding: 4px;
    }

    .custom-pagination-controls {
      padding: 12px;
      
      .pagination-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
      }

      .pagination-indicator {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }
  }

  @media (max-width: 480px) {
    --grid-gap: 2px;

    .custom-grid-container {
      padding: 2px;
    }

    .custom-grid-item {
      border-radius: 4px;

      &:hover {
        transform: none; /* Disable hover effects on mobile */
      }
    }
  }

  /* Animation for layout changes */
  .custom-grid-container {
    transition: grid-template-columns 0.3s ease, grid-template-rows 0.3s ease;
  }

  .custom-grid-item {
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Focus states for accessibility */
  .pagination-btn:focus,
  .pagination-indicator:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .custom-grid-item {
      border: 1px solid white;
    }

    .pagination-btn,
    .pagination-indicator {
      border: 1px solid white;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .custom-grid-item {
      transition: none;
      
      &:hover {
        transform: none;
      }
    }

    .custom-grid-container {
      transition: none;
    }

    .custom-grid-item {
      animation: none;
    }
  }
}

/* Dark theme adjustments */
[data-theme="dark"] .custom-grid-layout {
  .custom-grid-item {
    background: #2a2a2a;
  }

  .custom-pagination-controls {
    background: rgba(0, 0, 0, 0.3);
    border-top-color: rgba(255, 255, 255, 0.2);
  }
}

/* Light theme adjustments */
[data-theme="light"] .custom-grid-layout {
  .custom-grid-item {
    background: #f5f5f5;
  }

  .custom-pagination-controls {
    background: rgba(255, 255, 255, 0.1);
    border-top-color: rgba(0, 0, 0, 0.1);

    .pagination-btn,
    .pagination-indicator {
      color: #333;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}

/* Integration with DaakiaVC styles */
.lk-grid-layout-wrapper .daakia-custom-grid {
  height: 100%;
  width: 100%;
}

/* Ensure compatibility with existing VideoConference styles */
.lk-video-conference-inner .daakia-custom-grid {
  .custom-grid-item {
    /* Inherit any existing participant tile styles */
    border-radius: 8px;

    /* Ensure ParticipantTile fills the container properly */
    > div {
      width: 100%;
      height: 100%;
      border-radius: inherit;
    }
  }

  /* Style pagination to match DaakiaVC theme */
  .custom-pagination-controls {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .pagination-btn {
      background: rgba(255, 255, 255, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    .pagination-indicator {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.15);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      &.active {
        background: #007bff;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
      }
    }
  }
}
