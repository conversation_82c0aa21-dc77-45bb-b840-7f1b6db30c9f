# Custom GridLayout Component

A fully customizable grid layout component for LiveKit video conferencing, built to replace the default LiveKit GridLayout with enhanced features and full control.

## Features

✅ **Custom Grid Calculations** - Smart grid sizing based on participant count and container dimensions
✅ **Pagination Support** - Automatic pagination for large numbers of participants  
✅ **Responsive Design** - Adapts to different screen sizes and orientations
✅ **Visual Stability** - Smooth transitions and animations
✅ **Swipe Gestures** - Touch-friendly navigation on mobile devices
✅ **Accessibility** - Full keyboard navigation and screen reader support
✅ **Customizable Styling** - Complete control over appearance and layout
✅ **Performance Optimized** - Efficient rendering and memory usage

## Usage

### Basic Usage

```javascript
import GridLayoutCustom from './components/GridLayout/GridLayoutCustom';
import { TrackRefContext } from '@livekit/components-react';

// Replace the original GridLayout
<GridLayoutCustom tracks={tracks}>
  <TrackRefContext.Consumer>
    {(trackRef) => (
      <ParticipantTile
        trackRef={trackRef}
        // ... your props
      />
    )}
  </TrackRefContext.Consumer>
</GridLayoutCustom>
```

### Advanced Usage with Custom Options

```javascript
<GridLayoutCustom 
  tracks={tracks}
  enablePagination={true}
  enableSwipeGestures={true}
  maxTilesPerPage={9}
  className="my-custom-grid"
>
  <TrackRefContext.Consumer>
    {(trackRef) => (
      <ParticipantTile trackRef={trackRef} />
    )}
  </TrackRefContext.Consumer>
</GridLayoutCustom>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `tracks` | `TrackReference[]` | `[]` | Array of track references to display |
| `children` | `ReactNode` | - | Child component to render for each track |
| `className` | `string` | `''` | Additional CSS classes |
| `enablePagination` | `boolean` | `true` | Enable/disable pagination |
| `enableSwipeGestures` | `boolean` | `true` | Enable/disable swipe navigation |
| `maxTilesPerPage` | `number` | `auto` | Maximum tiles per page (auto-calculated if not provided) |

## Grid Layouts

The component automatically selects the best grid layout based on:
- Number of participants
- Container dimensions
- Aspect ratio optimization

Available layouts:
- 1x1 (1 participant)
- 2x1 (2 participants)
- 2x2 (3-4 participants)
- 3x2 (5-6 participants)
- 3x3 (7-9 participants)
- 4x3 (10-12 participants)
- 4x4 (13-16 participants)
- 5x4 (17-20 participants)
- 5x5 (21-25 participants)

## Customization

### CSS Custom Properties

```css
.custom-grid-layout {
  --grid-gap: 8px;                    /* Gap between grid items */
  --grid-item-aspect-ratio: 16/9;     /* Preferred aspect ratio */
}
```

### Custom Grid Layouts

You can define your own grid layouts by modifying the `GRID_LAYOUTS` array in the component:

```javascript
const CUSTOM_LAYOUTS = [
  { columns: 6, rows: 4, name: '6x4', minTiles: 21, maxTiles: 24 },
  // Add more custom layouts...
];
```

### Styling

The component uses SCSS for styling. Key classes:

- `.custom-grid-layout` - Main container
- `.custom-grid-container` - Grid container
- `.custom-grid-item` - Individual grid items
- `.custom-pagination-controls` - Pagination controls

## Integration with VideoConference

To replace the LiveKit GridLayout in your VideoConference component:

1. Import the custom component:
```javascript
import GridLayoutCustom from './components/GridLayout/GridLayoutCustom';
```

2. Replace the GridLayout usage:
```javascript
// Before
<GridLayout tracks={tracks}>
  <TrackRefContext.Consumer>
    {(trackRef) => <ParticipantTile trackRef={trackRef} />}
  </TrackRefContext.Consumer>
</GridLayout>

// After  
<GridLayoutCustom tracks={tracks}>
  <TrackRefContext.Consumer>
    {(trackRef) => <ParticipantTile trackRef={trackRef} />}
  </TrackRefContext.Consumer>
</GridLayoutCustom>
```

## Console Logging

The component includes detailed console logging for debugging:

```
🔧 GridLayoutCustom render: {
  totalTracks: 5,
  displayTracks: 4,
  layout: "2x2",
  currentPage: 1,
  totalPages: 2,
  containerSize: { width: 800, height: 600 }
}
```

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## Performance Notes

- Uses ResizeObserver for efficient container size tracking
- Implements visual stability to minimize layout shifts
- Optimized for 60fps animations
- Memory efficient with proper cleanup

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- High contrast mode support
- Reduced motion support
- ARIA labels for all interactive elements
